import { FC } from 'react';
import {
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    FormHelperText,
    Box
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import HinguaGrid from '../../CommonComponents/HinguaGrid';
import { BasicInfoModel, Gender } from '../../Types/ProfileTypes';

interface IBasicInfoFormProps {
    initialValues?: Partial<BasicInfoModel>;
    onSubmit: (values: BasicInfoModel) => void;
    onValidationChange?: (isValid: boolean) => void;
    isReadOnly?: boolean;
}

const validationSchema = Yup.object().shape({
    name: Yup.string()
        .min(2, 'Name must be at least 2 characters')
        .max(100, 'Name must be less than 100 characters')
        .required('Name is required'),
    gender: Yup.string()
        .oneOf(['MALE', 'FEMALE'], 'Please select a valid gender')
        .required('Gender is required'),
    dateOfBirth: Yup.date()
        .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old')
        .required('Date of birth is required'),
    placeOfBirth: Yup.string()
        .min(1, 'Place of birth is required')
        .required('Place of birth is required'),
    birthLatitude: Yup.number()
        .min(-90, 'Invalid latitude')
        .max(90, 'Invalid latitude'),
    birthLongitude: Yup.number()
        .min(-180, 'Invalid longitude')
        .max(180, 'Invalid longitude'),
    timeOfBirth: Yup.string(),
    timeZone: Yup.string()
});

const BasicInfoForm: FC<IBasicInfoFormProps> = ({
    initialValues = {},
    onSubmit,
    onValidationChange,
    isReadOnly = false
}) => {
    const formik = useFormik<BasicInfoModel>({
        initialValues: {
            name: '',
            gender: Gender.MALE,
            dateOfBirth: '',
            placeOfBirth: '',
            birthLatitude: undefined,
            birthLongitude: undefined,
            timeOfBirth: '',
            timeZone: '',
            ...initialValues
        } as BasicInfoModel,
        validationSchema,
        onSubmit: (values) => {
            onSubmit(values);
        },
        validate: (values) => {
            if (onValidationChange) {
                validationSchema.isValid(values).then(onValidationChange);
            }
        }
    });

    const timeZones = [
        'Asia/Kolkata', 'Asia/Mumbai', 'Asia/Delhi', 'Asia/Chennai', 'Asia/Bangalore',
        'UTC', 'America/New_York', 'Europe/London', 'Asia/Dubai'
    ];

    return (
        <Box component="form" onSubmit={formik.handleSubmit}>
            <HinguaGrid
                direction="column"
                spacing={3}
                gridItems={[
                    {
                        element: (
                            <TextField
                                fullWidth
                                label="Full Name"
                                name="name"
                                value={formik.values.name}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.name && Boolean(formik.errors.name)}
                                helperText={formik.touched.name && formik.errors.name}
                                disabled={isReadOnly}
                                required
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <FormControl
                                                fullWidth
                                                error={formik.touched.gender && Boolean(formik.errors.gender)}
                                                disabled={isReadOnly}
                                            >
                                                <InputLabel>Gender *</InputLabel>
                                                <Select
                                                    name="gender"
                                                    value={formik.values.gender}
                                                    onChange={formik.handleChange}
                                                    onBlur={formik.handleBlur}
                                                    label="Gender *"
                                                >
                                                    <MenuItem value={Gender.MALE}>Male</MenuItem>
                                                    <MenuItem value={Gender.FEMALE}>Female</MenuItem>
                                                </Select>
                                                {formik.touched.gender && formik.errors.gender && (
                                                    <FormHelperText>{formik.errors.gender}</FormHelperText>
                                                )}
                                            </FormControl>
                                        ),
                                        properties: { size: 6 }
                                    },
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Date of Birth"
                                                name="dateOfBirth"
                                                type="date"
                                                value={formik.values.dateOfBirth}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.dateOfBirth && Boolean(formik.errors.dateOfBirth)}
                                                helperText={formik.touched.dateOfBirth && formik.errors.dateOfBirth}
                                                disabled={isReadOnly}
                                                InputLabelProps={{ shrink: true }}
                                                required
                                            />
                                        ),
                                        properties: { size: 6 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <TextField
                                fullWidth
                                label="Place of Birth"
                                name="placeOfBirth"
                                value={formik.values.placeOfBirth}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.placeOfBirth && Boolean(formik.errors.placeOfBirth)}
                                helperText={formik.touched.placeOfBirth && formik.errors.placeOfBirth}
                                disabled={isReadOnly}
                                required
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Birth Latitude"
                                                name="birthLatitude"
                                                type="number"
                                                value={formik.values.birthLatitude || ''}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.birthLatitude && Boolean(formik.errors.birthLatitude)}
                                                helperText={formik.touched.birthLatitude && formik.errors.birthLatitude}
                                                disabled={isReadOnly}
                                                inputProps={{ min: -90, max: 90, step: 0.000001 }}
                                            />
                                        ),
                                        properties: { size: 6 }
                                    },
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Birth Longitude"
                                                name="birthLongitude"
                                                type="number"
                                                value={formik.values.birthLongitude || ''}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.birthLongitude && Boolean(formik.errors.birthLongitude)}
                                                helperText={formik.touched.birthLongitude && formik.errors.birthLongitude}
                                                disabled={isReadOnly}
                                                inputProps={{ min: -180, max: 180, step: 0.000001 }}
                                            />
                                        ),
                                        properties: { size: 6 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Time of Birth"
                                                name="timeOfBirth"
                                                type="time"
                                                value={formik.values.timeOfBirth || ''}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.timeOfBirth && Boolean(formik.errors.timeOfBirth)}
                                                helperText={formik.touched.timeOfBirth && formik.errors.timeOfBirth}
                                                disabled={isReadOnly}
                                                InputLabelProps={{ shrink: true }}
                                            />
                                        ),
                                        properties: { size: 6 }
                                    },
                                    {
                                        element: (
                                            <FormControl
                                                fullWidth
                                                error={formik.touched.timeZone && Boolean(formik.errors.timeZone)}
                                                disabled={isReadOnly}
                                            >
                                                <InputLabel>Time Zone</InputLabel>
                                                <Select
                                                    name="timeZone"
                                                    value={formik.values.timeZone || ''}
                                                    onChange={formik.handleChange}
                                                    onBlur={formik.handleBlur}
                                                    label="Time Zone"
                                                >
                                                    {timeZones.map((tz) => (
                                                        <MenuItem key={tz} value={tz}>
                                                            {tz}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                                {formik.touched.timeZone && formik.errors.timeZone && (
                                                    <FormHelperText>{formik.errors.timeZone}</FormHelperText>
                                                )}
                                            </FormControl>
                                        ),
                                        properties: { size: 6 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    }
                ]}
            />
        </Box>
    );
};

export default BasicInfoForm;

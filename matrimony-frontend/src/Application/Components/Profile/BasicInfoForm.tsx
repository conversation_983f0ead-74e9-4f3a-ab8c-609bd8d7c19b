import { FC } from 'react';
import {
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    FormHelperText,
    Box
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import HinguaGrid from '../../CommonComponents/HinguaGrid';
import { IBasicInfo } from '../../Types/ProfileTypes';

interface IBasicInfoFormProps {
    initialValues?: Partial<IBasicInfo>;
    onSubmit: (values: IBasicInfo) => void;
    onValidationChange?: (isValid: boolean) => void;
    isReadOnly?: boolean;
}

const validationSchema = Yup.object().shape({
    firstName: Yup.string()
        .min(2, 'First name must be at least 2 characters')
        .max(50, 'First name must be less than 50 characters')
        .required('First name is required'),
    lastName: Yup.string()
        .min(2, 'Last name must be at least 2 characters')
        .max(50, 'Last name must be less than 50 characters')
        .required('Last name is required'),
    dateOfBirth: Yup.date()
        .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old')
        .required('Date of birth is required'),
    gender: Yup.string()
        .oneOf(['MALE', 'FEMALE', 'OTHER'], 'Please select a valid gender')
        .required('Gender is required'),
    religion: Yup.string()
        .required('Religion is required'),
    caste: Yup.string()
        .required('Caste is required'),
    subCaste: Yup.string(),
    motherTongue: Yup.string()
        .required('Mother tongue is required'),
    maritalStatus: Yup.string()
        .oneOf(['NEVER_MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED'], 'Please select a valid marital status')
        .required('Marital status is required')
});

const BasicInfoForm: FC<IBasicInfoFormProps> = ({
    initialValues = {},
    onSubmit,
    onValidationChange,
    isReadOnly = false
}) => {
    const formik = useFormik<IBasicInfo>({
        initialValues: {
            firstName: '',
            lastName: '',
            dateOfBirth: '',
            gender: 'MALE',
            religion: '',
            caste: '',
            subCaste: '',
            motherTongue: '',
            maritalStatus: 'NEVER_MARRIED',
            ...initialValues
        } as IBasicInfo,
        validationSchema,
        onSubmit: (values) => {
            onSubmit(values);
        },
        validate: (values) => {
            if (onValidationChange) {
                validationSchema.isValid(values).then(onValidationChange);
            }
        }
    });

    const religions = [
        'Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain', 'Parsi', 'Jewish', 'Other'
    ];

    const languages = [
        'Tamil', 'Telugu', 'Kannada', 'Malayalam', 'Hindi', 'Marathi', 'Gujarati', 'Bengali', 
        'Punjabi', 'Oriya', 'Assamese', 'Urdu', 'English', 'Other'
    ];

    return (
        <Box component="form" onSubmit={formik.handleSubmit}>
            <HinguaGrid
                direction="column"
                spacing={3}
                gridItems={[
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="First Name"
                                                name="firstName"
                                                value={formik.values.firstName}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                                                helperText={formik.touched.firstName && formik.errors.firstName}
                                                disabled={isReadOnly}
                                                required
                                            />
                                        ),
                                        properties: { size: 6 }
                                    },
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Last Name"
                                                name="lastName"
                                                value={formik.values.lastName}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                                                helperText={formik.touched.lastName && formik.errors.lastName}
                                                disabled={isReadOnly}
                                                required
                                            />
                                        ),
                                        properties: { size: 6 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Date of Birth"
                                                name="dateOfBirth"
                                                type="date"
                                                value={formik.values.dateOfBirth}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.dateOfBirth && Boolean(formik.errors.dateOfBirth)}
                                                helperText={formik.touched.dateOfBirth && formik.errors.dateOfBirth}
                                                disabled={isReadOnly}
                                                InputLabelProps={{ shrink: true }}
                                                required
                                            />
                                        ),
                                        properties: { size: 6 }
                                    },
                                    {
                                        element: (
                                            <FormControl 
                                                fullWidth 
                                                error={formik.touched.gender && Boolean(formik.errors.gender)}
                                                disabled={isReadOnly}
                                            >
                                                <InputLabel>Gender *</InputLabel>
                                                <Select
                                                    name="gender"
                                                    value={formik.values.gender}
                                                    onChange={formik.handleChange}
                                                    onBlur={formik.handleBlur}
                                                    label="Gender *"
                                                >
                                                    <MenuItem value="MALE">Male</MenuItem>
                                                    <MenuItem value="FEMALE">Female</MenuItem>
                                                    <MenuItem value="OTHER">Other</MenuItem>
                                                </Select>
                                                {formik.touched.gender && formik.errors.gender && (
                                                    <FormHelperText>{formik.errors.gender}</FormHelperText>
                                                )}
                                            </FormControl>
                                        ),
                                        properties: { size: 6 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <FormControl 
                                                fullWidth 
                                                error={formik.touched.religion && Boolean(formik.errors.religion)}
                                                disabled={isReadOnly}
                                            >
                                                <InputLabel>Religion *</InputLabel>
                                                <Select
                                                    name="religion"
                                                    value={formik.values.religion}
                                                    onChange={formik.handleChange}
                                                    onBlur={formik.handleBlur}
                                                    label="Religion *"
                                                >
                                                    {religions.map((religion) => (
                                                        <MenuItem key={religion} value={religion}>
                                                            {religion}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                                {formik.touched.religion && formik.errors.religion && (
                                                    <FormHelperText>{formik.errors.religion}</FormHelperText>
                                                )}
                                            </FormControl>
                                        ),
                                        properties: { size: 4 }
                                    },
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Caste"
                                                name="caste"
                                                value={formik.values.caste}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.caste && Boolean(formik.errors.caste)}
                                                helperText={formik.touched.caste && formik.errors.caste}
                                                disabled={isReadOnly}
                                                required
                                            />
                                        ),
                                        properties: { size: 4 }
                                    },
                                    {
                                        element: (
                                            <TextField
                                                fullWidth
                                                label="Sub Caste"
                                                name="subCaste"
                                                value={formik.values.subCaste || ''}
                                                onChange={formik.handleChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.subCaste && Boolean(formik.errors.subCaste)}
                                                helperText={formik.touched.subCaste && formik.errors.subCaste}
                                                disabled={isReadOnly}
                                            />
                                        ),
                                        properties: { size: 4 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    },
                    {
                        element: (
                            <HinguaGrid
                                direction="row"
                                spacing={2}
                                gridItems={[
                                    {
                                        element: (
                                            <FormControl 
                                                fullWidth 
                                                error={formik.touched.motherTongue && Boolean(formik.errors.motherTongue)}
                                                disabled={isReadOnly}
                                            >
                                                <InputLabel>Mother Tongue *</InputLabel>
                                                <Select
                                                    name="motherTongue"
                                                    value={formik.values.motherTongue}
                                                    onChange={formik.handleChange}
                                                    onBlur={formik.handleBlur}
                                                    label="Mother Tongue *"
                                                >
                                                    {languages.map((language) => (
                                                        <MenuItem key={language} value={language}>
                                                            {language}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                                {formik.touched.motherTongue && formik.errors.motherTongue && (
                                                    <FormHelperText>{formik.errors.motherTongue}</FormHelperText>
                                                )}
                                            </FormControl>
                                        ),
                                        properties: { size: 6 }
                                    },
                                    {
                                        element: (
                                            <FormControl 
                                                fullWidth 
                                                error={formik.touched.maritalStatus && Boolean(formik.errors.maritalStatus)}
                                                disabled={isReadOnly}
                                            >
                                                <InputLabel>Marital Status *</InputLabel>
                                                <Select
                                                    name="maritalStatus"
                                                    value={formik.values.maritalStatus}
                                                    onChange={formik.handleChange}
                                                    onBlur={formik.handleBlur}
                                                    label="Marital Status *"
                                                >
                                                    <MenuItem value="NEVER_MARRIED">Never Married</MenuItem>
                                                    <MenuItem value="DIVORCED">Divorced</MenuItem>
                                                    <MenuItem value="WIDOWED">Widowed</MenuItem>
                                                    <MenuItem value="SEPARATED">Separated</MenuItem>
                                                </Select>
                                                {formik.touched.maritalStatus && formik.errors.maritalStatus && (
                                                    <FormHelperText>{formik.errors.maritalStatus}</FormHelperText>
                                                )}
                                            </FormControl>
                                        ),
                                        properties: { size: 6 }
                                    }
                                ]}
                            />
                        ),
                        properties: { size: 12 }
                    }
                ]}
            />
        </Box>
    );
};

export default BasicInfoForm;

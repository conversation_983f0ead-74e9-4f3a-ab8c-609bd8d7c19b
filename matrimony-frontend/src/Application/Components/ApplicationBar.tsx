import React, {use<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ventHand<PERSON>} from 'react';
import {
    AppBar,
    Box,
    IconButton,
    Grid2,
    Typography,
    Avatar,
    Button,
    Menu,
    MenuItem,
    ListItemIcon,
    ListItemText
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import {useAppSelector} from '../Globals/redux/hooks';

import LoginIcon from '@mui/icons-material/Login';
import LogoutIcon from '@mui/icons-material/Logout';

import logo from "../../assets/logo.jpeg"
import {useTranslation} from "react-i18next";
import { Outlet } from "react-router-dom";

function stringToColor(string: string) {
    let hash = 0;
    let i;

    /* eslint-disable no-bitwise */
    for (i = 0; i < string.length; i += 1) {
        hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';

    for (i = 0; i < 3; i += 1) {
        const value = (hash >> (i * 8)) & 0xff;
        color += `00${value.toString(16)}`.slice(-2);
    }
    /* eslint-enable no-bitwise */

    return color;
}

function stringAvatar(name: string) {
    return {
        sx: {
            bgcolor: stringToColor(name),
        },
        children: `${name.split(' ')[0][0]}${name.split(' ')[1][0]}`,
    };
}

const ApplicationBar: React.FC<unknown> = () => {

    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);
    const { t } = useTranslation();

    const handleClick:MouseEventHandler<any> = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleSubmit: React.MouseEventHandler<HTMLLIElement> = async () => {
        setAnchorEl(null);
        const form = document.createElement('form');
        form.setAttribute('method','post')
        form.setAttribute('action','/logout')
        document.getElementsByTagName("body")[0].appendChild(form)
        form.submit();
    };

    const login = useAppSelector(state => state.login.value)

    const renderLeftBar = useMemo(() => {
        return <Grid2 container spacing={1} alignItems="center">
            <Grid2 key="options">
                <IconButton color="inherit" size="large">
                    <MenuIcon fontSize="large"/>
                </IconButton>
            </Grid2>
            <Grid2 key={"logo"}>
                <Avatar variant="rounded" src={logo}/>
            </Grid2>
            <Grid2 key="title">
                <Typography variant="h3">{t("title")}</Typography>
            </Grid2>
        </Grid2>
    }, [t])

    const renderRightBar = useMemo(() => {
        return <Grid2 container spacing={1}>
            {
                !(login.isLoggedIn) &&
                <Grid2 key="sign-in">
                    <Button
                        variant={"text"}
                        href={"/login"}
                        startIcon={<LoginIcon/>}
                        sx={{color: "white"}}
                    >
                        {t("login")}
                    </Button>
                </Grid2>
            }
            {
                login.isLoggedIn &&
                <Fragment>
                    <Grid2 onClick={handleClick} key="profile-picture">
                        {
                            login.profilePictureURL ?
                                <Avatar src={login.profilePictureURL}/> :
                                <Avatar {...stringAvatar(login.name || "")} />
                        }
                    </Grid2>
                    <Grid2 key="logout">

                    </Grid2>
                    <Menu
                        id="basic-menu"
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                    >
                        <MenuItem onClick={handleSubmit}>
                            <ListItemIcon>
                                <LogoutIcon/>
                            </ListItemIcon>
                            <ListItemText>
                                {"Log Out"}
                            </ListItemText>
                        </MenuItem>
                    </Menu>
                </Fragment>
            }
        </Grid2>
    }, [anchorEl, login, open, t])

    return <React.Fragment>
        <AppBar color="primary" style={{zIndex: 100, backgroundColor: "#B6AA8E"}} position="static">
            <Box sx={{padding: "5px", paddingLeft: "10px", paddingRight: "25px"}}>
                <Grid2 container justifyContent="space-between" alignItems="center">
                    <Grid2 key={"left"}>
                        {renderLeftBar}
                    </Grid2>
                    <Grid2 key={"right"}>
                        {renderRightBar}
                    </Grid2>
                </Grid2>
            </Box>
        </AppBar>
        <div className={"page-content"}>
        <Outlet />
        </div>
    </React.Fragment>;
};

export default ApplicationBar;
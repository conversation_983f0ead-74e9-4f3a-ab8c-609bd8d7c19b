import React, { FC, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../Globals/redux/hooks';
import { fetchUserProfiles } from '../Globals/redux/profileSlice';
import { ProfileState } from '../Types/ProfileTypes';
import ProfileListPage from '../Pages/ProfileListPage';
import ProfileStepperPage from '../Pages/ProfileStepperPage';

/**
 * ProfileDashboard handles the main profile workflow logic:
 * - If no profiles exist, redirect to create profile workflow
 * - If user has one incomplete profile, take to next step for completion
 * - If user has completed profile, go to profile home page
 * - Otherwise, show profile list
 */
const ProfileDashboard: FC = () => {
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    
    const {
        profiles,
        isLoading,
        totalCount
    } = useAppSelector(state => state.profile);

    const login = useAppSelector(state => state.login.value);

    useEffect(() => {
        if (login.loggedIn) {
            dispatch(fetchUserProfiles());
        }
    }, [dispatch, login.loggedIn]);

    useEffect(() => {
        if (!isLoading && login.loggedIn) {
            // No profiles - redirect to create profile workflow
            if (totalCount === 0) {
                navigate('/profile/create');
                return;
            }

            // Check for incomplete profiles
            const incompleteProfiles = profiles.filter(
                profile => profile.profileState !== ProfileState.COMPLETED
            );

            // If user has only one incomplete profile, take to next step
            if (incompleteProfiles.length === 1 && profiles.length === 1) {
                const incompleteProfile = incompleteProfiles[0];
                navigate(`/profile/${incompleteProfile.id}/create`);
                return;
            }

            // If user has at least one completed profile, show the first completed one
            const completedProfiles = profiles.filter(
                profile => profile.profileState === ProfileState.COMPLETED
            );

            if (completedProfiles.length > 0) {
                navigate(`/profile/${completedProfiles[0].id}/home`);
                return;
            }

            // Otherwise, show profile list (multiple incomplete profiles or mixed)
            navigate('/profiles');
        }
    }, [profiles, totalCount, isLoading, login.loggedIn, navigate]);

    // Show loading or profile list as fallback
    return <ProfileListPage />;
};

export default ProfileDashboard;

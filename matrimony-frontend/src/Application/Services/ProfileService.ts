import axios from 'axios';
import { 
    IProfile, 
    IProfileListResponse, 
    ICreateProfileRequest, 
    IUpdateProfileRequest,
    ProfileState 
} from '../Types/ProfileTypes';

export class ProfileService {
    private static readonly BASE_URL = '/api/profile';

    /**
     * Get all profiles for the current user
     */
    static async getUserProfiles(): Promise<IProfileListResponse> {
        const response = await axios.get(`${this.BASE_URL}/user-profiles`);
        return response.data;
    }

    /**
     * Get a specific profile by ID
     */
    static async getProfile(profileId: string): Promise<IProfile> {
        const response = await axios.get(`${this.BASE_URL}/${profileId}`);
        return response.data;
    }

    /**
     * Create a new profile
     */
    static async createProfile(profileData: ICreateProfileRequest): Promise<IProfile> {
        const response = await axios.post(`${this.BASE_URL}/create`, profileData);
        return response.data;
    }

    /**
     * Update an existing profile
     */
    static async updateProfile(profileData: IUpdateProfileRequest): Promise<IProfile> {
        const response = await axios.put(`${this.BASE_URL}/update`, profileData);
        return response.data;
    }

    /**
     * Update profile state (for stepper progression)
     */
    static async updateProfileState(profileId: string, newState: ProfileState): Promise<IProfile> {
        const response = await axios.patch(`${this.BASE_URL}/${profileId}/state`, {
            profileState: newState
        });
        return response.data;
    }

    /**
     * Delete a profile
     */
    static async deleteProfile(profileId: string): Promise<void> {
        await axios.delete(`${this.BASE_URL}/${profileId}`);
    }

    /**
     * Upload profile picture
     */
    static async uploadProfilePicture(profileId: string, file: File): Promise<string> {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('profileId', profileId);

        const response = await axios.post(`${this.BASE_URL}/${profileId}/upload-picture`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data.profilePictureUrl;
    }

    /**
     * Get profile completion percentage
     */
    static async getProfileCompletion(profileId: string): Promise<number> {
        const response = await axios.get(`${this.BASE_URL}/${profileId}/completion`);
        return response.data.completionPercentage;
    }

    /**
     * Activate/Deactivate profile
     */
    static async toggleProfileStatus(profileId: string, isActive: boolean): Promise<IProfile> {
        const response = await axios.patch(`${this.BASE_URL}/${profileId}/status`, {
            isActive
        });
        return response.data;
    }

    /**
     * Get profiles for matching (search functionality)
     */
    static async searchProfiles(searchCriteria: any): Promise<IProfileListResponse> {
        const response = await axios.post(`${this.BASE_URL}/search`, searchCriteria);
        return response.data;
    }

    /**
     * Save profile step data (for stepper workflow)
     */
    static async saveProfileStep(
        profileId: string, 
        stepData: any, 
        currentState: ProfileState
    ): Promise<IProfile> {
        const response = await axios.patch(`${this.BASE_URL}/${profileId}/step`, {
            stepData,
            currentState
        });
        return response.data;
    }

    /**
     * Validate profile data for a specific step
     */
    static async validateProfileStep(
        stepData: any, 
        profileState: ProfileState
    ): Promise<{ isValid: boolean; errors: string[] }> {
        const response = await axios.post(`${this.BASE_URL}/validate-step`, {
            stepData,
            profileState
        });
        return response.data;
    }
}

import axios from 'axios';
import {
    TblProfiles,
    BasicInfoModel,
    PersonalInfoModel,
    EducationCareerModel,
    FamilyInfoModel,
    ContactInfoModel,
    CreateProfileResponse,
    AuthStatusResponse,
    RegisterUserModel,
    AuthenticationRequest
} from '../Types/ProfileTypes';

export class ProfileService {
    private static readonly BASE_URL = '/api/private/profiles';

    /**
     * Get all profiles for the current user
     */
    static async getUserProfiles(): Promise<TblProfiles[]> {
        const response = await axios.get(`${this.BASE_URL}/list`);
        return response.data;
    }

    /**
     * Get a specific profile by ID
     */
    static async getProfile(profileId: number): Promise<TblProfiles> {
        const response = await axios.get(`${this.BASE_URL}/get/${profileId}`);
        return response.data;
    }

    /**
     * Create a new profile with basic info
     */
    static async createProfile(basicInfo: BasicInfoModel): Promise<CreateProfileResponse> {
        const response = await axios.post(`${this.BASE_URL}/create`, basicInfo);
        return response.data;
    }

    /**
     * Update personal information
     */
    static async updatePersonalInfo(profileId: number, personalInfo: PersonalInfoModel): Promise<void> {
        await axios.put(`${this.BASE_URL}/${profileId}/personal-info`, personalInfo);
    }

    /**
     * Update education and career information
     */
    static async updateEducationCareer(profileId: number, educationCareer: EducationCareerModel): Promise<void> {
        await axios.put(`${this.BASE_URL}/${profileId}/education-career`, educationCareer);
    }

    /**
     * Update family information
     */
    static async updateFamilyInfo(profileId: number, familyInfo: FamilyInfoModel): Promise<void> {
        await axios.put(`${this.BASE_URL}/${profileId}/family-info`, familyInfo);
    }

    /**
     * Update contact information
     */
    static async updateContactInfo(profileId: number, contactInfo: ContactInfoModel): Promise<void> {
        await axios.put(`${this.BASE_URL}/${profileId}/contact-info`, contactInfo);
    }

}

// Auth Service
export class AuthService {
    private static readonly BASE_URL = '/api/auth';

    /**
     * Get authentication status
     */
    static async getAuthStatus(): Promise<AuthStatusResponse> {
        const response = await axios.post(`${this.BASE_URL}/status`);
        return response.data;
    }

    /**
     * Register new user
     */
    static async register(userData: RegisterUserModel): Promise<{ [key: string]: string }> {
        const response = await axios.post(`${this.BASE_URL}/register`, userData);
        return response.data;
    }

    /**
     * Login user
     */
    static async login(credentials: AuthenticationRequest): Promise<{ [key: string]: string }> {
        const response = await axios.post(`${this.BASE_URL}/login`, credentials);
        return response.data;
    }
}

// Profile Management Types

export enum ProfileState {
    BASIC_INFO = 'BASIC_INFO',
    PERSONAL_INFO = 'PERSONAL_INFO',
    EDUCATION_CAREER = 'EDUCATION_CAREER',
    FAMILY_INFO = 'FAMILY_INFO',
    CONTACT_INFO = 'CONTACT_INFO',
    COMPLETED = 'COMPLETED'
}

export interface IBasicInfo {
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: 'MALE' | 'FEMALE' | 'OTHER';
    religion: string;
    caste: string;
    subCaste?: string;
    motherTongue: string;
    maritalStatus: 'NEVER_MARRIED' | 'DIVORCED' | 'WIDOWED' | 'SEPARATED';
}

export interface IPersonalInfo {
    height: number; // in cm
    weight?: number; // in kg
    bodyType: 'SLIM' | 'AVERAGE' | 'ATHLETIC' | 'HEAVY';
    complexion: 'VERY_FAIR' | 'FAIR' | 'WHEATISH' | 'DARK' | 'VERY_DARK';
    physicalStatus: 'NORMAL' | 'PHYSICALLY_CHALLENGED';
    bloodGroup?: string;
    diet: 'VEGETARIAN' | 'NON_VEGETARIAN' | 'EGGETARIAN' | 'VEGAN';
    smoking: 'NEVER' | 'OCCASIONALLY' | 'REGULARLY';
    drinking: 'NEVER' | 'OCCASIONALLY' | 'REGULARLY';
    hobbies?: string[];
    interests?: string[];
    aboutMe?: string;
}

export interface IEducationCareer {
    highestEducation: string;
    educationDetails?: string;
    occupation: string;
    employedIn: 'GOVERNMENT' | 'PRIVATE' | 'BUSINESS' | 'DEFENSE' | 'SELF_EMPLOYED' | 'NOT_WORKING';
    annualIncome?: number;
    workingCity?: string;
    workingState?: string;
    workingCountry?: string;
    companyName?: string;
    designation?: string;
    workExperience?: number; // in years
}

export interface IFamilyInfo {
    fatherName: string;
    fatherOccupation?: string;
    motherName: string;
    motherOccupation?: string;
    numberOfBrothers?: number;
    numberOfSisters?: number;
    marriedBrothers?: number;
    marriedSisters?: number;
    familyType: 'NUCLEAR' | 'JOINT';
    familyStatus: 'MIDDLE_CLASS' | 'UPPER_MIDDLE_CLASS' | 'RICH' | 'AFFLUENT';
    familyValues: 'ORTHODOX' | 'TRADITIONAL' | 'MODERATE' | 'LIBERAL';
    aboutFamily?: string;
}

export interface IContactInfo {
    email: string;
    mobile: string;
    alternateEmail?: string;
    alternateMobile?: string;
    address: string;
    city: string;
    state: string;
    country: string;
    pincode: string;
    timeToCall?: string;
    preferredContactMethod: 'EMAIL' | 'PHONE' | 'BOTH';
}

export interface IProfile {
    id?: string;
    userId: string;
    profileState: ProfileState;
    basicInfo?: IBasicInfo;
    personalInfo?: IPersonalInfo;
    educationCareer?: IEducationCareer;
    familyInfo?: IFamilyInfo;
    contactInfo?: IContactInfo;
    profilePicture?: string;
    createdAt?: string;
    updatedAt?: string;
    isActive: boolean;
    completionPercentage: number;
}

export interface IProfileListResponse {
    profiles: IProfile[];
    totalCount: number;
}

export interface ICreateProfileRequest {
    basicInfo?: Partial<IBasicInfo>;
    personalInfo?: Partial<IPersonalInfo>;
    educationCareer?: Partial<IEducationCareer>;
    familyInfo?: Partial<IFamilyInfo>;
    contactInfo?: Partial<IContactInfo>;
}

export interface IUpdateProfileRequest extends ICreateProfileRequest {
    profileId: string;
    profileState?: ProfileState;
}

export interface IProfileStepperState {
    currentStep: number;
    profile: IProfile | null;
    isLoading: boolean;
    error: string | null;
    isEditing: boolean;
}

// Stepper configuration
export const PROFILE_STEPS = [
    {
        label: 'Basic Information',
        state: ProfileState.BASIC_INFO,
        description: 'Personal details and background'
    },
    {
        label: 'Personal Information',
        state: ProfileState.PERSONAL_INFO,
        description: 'Physical attributes and lifestyle'
    },
    {
        label: 'Education & Career',
        state: ProfileState.EDUCATION_CAREER,
        description: 'Educational and professional details'
    },
    {
        label: 'Family Information',
        state: ProfileState.FAMILY_INFO,
        description: 'Family background and values'
    },
    {
        label: 'Contact Information',
        state: ProfileState.CONTACT_INFO,
        description: 'Contact details and preferences'
    }
];

export const getStepIndex = (state: ProfileState): number => {
    return PROFILE_STEPS.findIndex(step => step.state === state);
};

export const getNextState = (currentState: ProfileState): ProfileState | null => {
    const currentIndex = getStepIndex(currentState);
    if (currentIndex === -1 || currentIndex === PROFILE_STEPS.length - 1) {
        return ProfileState.COMPLETED;
    }
    return PROFILE_STEPS[currentIndex + 1].state;
};

export const getPreviousState = (currentState: ProfileState): ProfileState | null => {
    const currentIndex = getStepIndex(currentState);
    if (currentIndex <= 0) {
        return null;
    }
    return PROFILE_STEPS[currentIndex - 1].state;
};

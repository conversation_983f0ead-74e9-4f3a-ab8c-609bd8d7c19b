// Profile Management Types - Based on API Specification

export enum ProfileCompletenessStep {
    BASIC_INFO = 'BASIC_INFO',
    PERSONAL_INFO = 'PERSONAL_INFO',
    EDUCATION_CAREER = 'EDUCATION_CAREER',
    FAMILY_INFO = 'FAMILY_INFO',
    CONTACT_INFO = 'CONTACT_INFO',
    COMPLETED = 'COMPLETED'
}

export enum Gender {
    MALE = 'MALE',
    FEMALE = 'FEMALE'
}

export enum MaritalStatus {
    NEVER_MARRIED = 'NEVER_MARRIED',
    DIVORCED = 'DIVORCED',
    WIDOWED = 'WIDOWED',
    SEPARATED = 'SEPARATED'
}

export enum Complexion {
    VERY_FAIR = 'VERY_FAIR',
    FAIR = 'FAIR',
    WHEATISH = 'WHEATISH',
    BROWN = 'BROWN',
    DARK = 'DARK'
}

export enum BloodGroup {
    A_POSITIVE = 'A_POSITIVE',
    A_NEGATIVE = 'A_NEGATIVE',
    B_POSITIVE = 'B_POSITIVE',
    B_NEGATIVE = 'B_NEGATIVE',
    AB_POSITIVE = 'AB_POSITIVE',
    AB_NEGATIVE = 'AB_NEGATIVE',
    O_POSITIVE = 'O_POSITIVE',
    O_NEGATIVE = 'O_NEGATIVE',
    BOMBAY_HH = 'BOMBAY_HH'
}

export enum EducationDegree {
    HIGH_SCHOOL = 'HIGH_SCHOOL',
    BACHELORS = 'BACHELORS',
    MASTERS = 'MASTERS',
    DOCTORATE = 'DOCTORATE',
    DIPLOMA = 'DIPLOMA',
    OTHER = 'OTHER'
}

export enum IncomeRange {
    BELOW_1L = 'BELOW_1L',
    L1_3L = 'L1_3L',
    L3_5L = 'L3_5L',
    L5_10L = 'L5_10L',
    L10_25L = 'L10_25L',
    L25_50L = 'L25_50L',
    L50_1CR = 'L50_1CR',
    ABOVE_1CR = 'ABOVE_1CR'
}

// API Model Interfaces - Based on OpenAPI Specification

export interface BasicInfoModel {
    name: string;
    gender: Gender;
    dateOfBirth: string; // format: date
    placeOfBirth: string;
    birthLatitude?: number;
    birthLongitude?: number;
    timeOfBirth?: string;
    timeZone?: string;
}

export interface PersonalInfoModel {
    maritalStatus: MaritalStatus;
    complexion?: Complexion;
    heightCm?: number; // min: 100, max: 250
    weightKg?: number; // min: 30, max: 200
    bloodGroup?: BloodGroup;
    eyeWear?: boolean;
}

export interface EducationCareerModel {
    educationDegree?: EducationDegree;
    educationDetails?: string;
    profession?: string;
    company?: string;
    incomeRange?: IncomeRange;
    placeOfStay?: string;
}

export interface FamilyInfoModel {
    fatherName?: string;
    motherName?: string;
    fatherAlive?: boolean;
    motherAlive?: boolean;
    fatherOccupation?: string;
    motherOccupation?: string;
    sistersCount?: number; // min: 0, max: 10
    brothersCount?: number; // min: 0, max: 10
}

export interface ContactInfoModel {
    address?: string;
    city?: string;
    state?: string;
    country: string; // required, minLength: 1
    pinCode?: string;
    fatherMobile?: string; // pattern: "^\\d{10}$"
    motherMobile?: string; // pattern: "^\\d{10}$"
}

// Main Profile Entity - Based on TblProfiles schema
export interface TblProfiles {
    id?: number;
    userId?: number;
    name?: string;
    gender?: Gender;
    dateOfBirth?: string; // format: date
    placeOfBirth?: string;
    birthLatitude?: number;
    birthLongitude?: number;
    timeOfBirth?: string;
    timeZone?: string;
    maritalStatus?: MaritalStatus;
    complexion?: Complexion;
    heightCm?: number;
    weightKg?: number;
    bloodGroup?: BloodGroup;
    eyeWear?: boolean;
    educationDegree?: EducationDegree;
    educationDetails?: string;
    profession?: string;
    company?: string;
    incomeRange?: IncomeRange;
    placeOfStay?: string;
    fatherName?: string;
    motherName?: string;
    fatherAlive?: boolean;
    motherAlive?: boolean;
    fatherOccupation?: string;
    motherOccupation?: string;
    sistersCount?: number;
    brothersCount?: number;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    pinCode?: string;
    fatherMobile?: string;
    motherMobile?: string;
    profileCompletenessStep?: ProfileCompletenessStep;
    createdAt?: string; // format: date-time
    updatedAt?: string; // format: date-time
}

// Auth Models
export interface AuthStatusResponse {
    profilePictureURL?: string;
    name?: string;
    loggedIn: boolean;
}

export interface RegisterUserModel {
    name: string;
    email: string; // pattern: email
    mobile: string; // pattern: "^\\d{10}$"
    password: string; // pattern: complex password
}

export interface AuthenticationRequest {
    username?: string;
    password?: string;
}

// Response Types
export interface CreateProfileResponse {
    [key: string]: number; // profileId
}

// Helper Types
export interface IProfileStepperState {
    currentStep: number;
    profile: TblProfiles | null;
    isLoading: boolean;
    error: string | null;
    isEditing: boolean;
}

// Stepper configuration
export const PROFILE_STEPS = [
    {
        label: 'Basic Information',
        state: ProfileCompletenessStep.BASIC_INFO,
        description: 'Personal details and background'
    },
    {
        label: 'Personal Information',
        state: ProfileCompletenessStep.PERSONAL_INFO,
        description: 'Physical attributes and lifestyle'
    },
    {
        label: 'Education & Career',
        state: ProfileCompletenessStep.EDUCATION_CAREER,
        description: 'Educational and professional details'
    },
    {
        label: 'Family Information',
        state: ProfileCompletenessStep.FAMILY_INFO,
        description: 'Family background and values'
    },
    {
        label: 'Contact Information',
        state: ProfileCompletenessStep.CONTACT_INFO,
        description: 'Contact details and preferences'
    }
];

export const getStepIndex = (state: ProfileCompletenessStep): number => {
    return PROFILE_STEPS.findIndex(step => step.state === state);
};

export const getNextState = (currentState: ProfileCompletenessStep): ProfileCompletenessStep | null => {
    const currentIndex = getStepIndex(currentState);
    if (currentIndex === -1 || currentIndex === PROFILE_STEPS.length - 1) {
        return ProfileCompletenessStep.COMPLETED;
    }
    return PROFILE_STEPS[currentIndex + 1].state;
};

export const getPreviousState = (currentState: ProfileCompletenessStep): ProfileCompletenessStep | null => {
    const currentIndex = getStepIndex(currentState);
    if (currentIndex <= 0) {
        return null;
    }
    return PROFILE_STEPS[currentIndex - 1].state;
};

import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { IProfile, IProfileListResponse, ProfileState } from "../../Types/ProfileTypes";
import { ProfileService } from "../../Services/ProfileService";

interface IProfileSliceState {
    profiles: IProfile[];
    currentProfile: IProfile | null;
    isLoading: boolean;
    error: string | null;
    totalCount: number;
    currentStep: number;
    isEditing: boolean;
}

const initialState: IProfileSliceState = {
    profiles: [],
    currentProfile: null,
    isLoading: false,
    error: null,
    totalCount: 0,
    currentStep: 0,
    isEditing: false
};

// Async thunks
export const fetchUserProfiles = createAsyncThunk(
    'profile/fetchUserProfiles',
    async (_, { rejectWithValue }) => {
        try {
            const response = await ProfileService.getUserProfiles();
            return response;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch profiles');
        }
    }
);

export const fetchProfile = createAsyncThunk(
    'profile/fetchProfile',
    async (profileId: string, { rejectWithValue }) => {
        try {
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch profile');
        }
    }
);

export const createProfile = createAsyncThunk(
    'profile/createProfile',
    async (profileData: any, { rejectWithValue }) => {
        try {
            const profile = await ProfileService.createProfile(profileData);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to create profile');
        }
    }
);

export const updateProfile = createAsyncThunk(
    'profile/updateProfile',
    async (profileData: any, { rejectWithValue }) => {
        try {
            const profile = await ProfileService.updateProfile(profileData);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to update profile');
        }
    }
);

export const updateProfileState = createAsyncThunk(
    'profile/updateProfileState',
    async ({ profileId, newState }: { profileId: string; newState: ProfileState }, { rejectWithValue }) => {
        try {
            const profile = await ProfileService.updateProfileState(profileId, newState);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to update profile state');
        }
    }
);

export const saveProfileStep = createAsyncThunk(
    'profile/saveProfileStep',
    async ({ profileId, stepData, currentState }: { 
        profileId: string; 
        stepData: any; 
        currentState: ProfileState 
    }, { rejectWithValue }) => {
        try {
            const profile = await ProfileService.saveProfileStep(profileId, stepData, currentState);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to save profile step');
        }
    }
);

const profileSlice = createSlice({
    name: "profile",
    initialState,
    reducers: {
        setCurrentProfile: (state, action: PayloadAction<IProfile | null>) => {
            state.currentProfile = action.payload;
        },
        setCurrentStep: (state, action: PayloadAction<number>) => {
            state.currentStep = action.payload;
        },
        setIsEditing: (state, action: PayloadAction<boolean>) => {
            state.isEditing = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        },
        resetProfileState: (state) => {
            state.currentProfile = null;
            state.currentStep = 0;
            state.isEditing = false;
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        // Fetch user profiles
        builder
            .addCase(fetchUserProfiles.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchUserProfiles.fulfilled, (state, action: PayloadAction<IProfileListResponse>) => {
                state.isLoading = false;
                state.profiles = action.payload.profiles;
                state.totalCount = action.payload.totalCount;
            })
            .addCase(fetchUserProfiles.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Fetch single profile
        builder
            .addCase(fetchProfile.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchProfile.fulfilled, (state, action: PayloadAction<IProfile>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
            })
            .addCase(fetchProfile.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Create profile
        builder
            .addCase(createProfile.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(createProfile.fulfilled, (state, action: PayloadAction<IProfile>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
                state.profiles.push(action.payload);
                state.totalCount += 1;
            })
            .addCase(createProfile.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Update profile
        builder
            .addCase(updateProfile.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(updateProfile.fulfilled, (state, action: PayloadAction<IProfile>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
                const index = state.profiles.findIndex(p => p.id === action.payload.id);
                if (index !== -1) {
                    state.profiles[index] = action.payload;
                }
            })
            .addCase(updateProfile.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Update profile state
        builder
            .addCase(updateProfileState.fulfilled, (state, action: PayloadAction<IProfile>) => {
                state.currentProfile = action.payload;
                const index = state.profiles.findIndex(p => p.id === action.payload.id);
                if (index !== -1) {
                    state.profiles[index] = action.payload;
                }
            });

        // Save profile step
        builder
            .addCase(saveProfileStep.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(saveProfileStep.fulfilled, (state, action: PayloadAction<IProfile>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
            })
            .addCase(saveProfileStep.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });
    }
});

export const { 
    setCurrentProfile, 
    setCurrentStep, 
    setIsEditing, 
    clearError, 
    resetProfileState 
} = profileSlice.actions;

export default profileSlice.reducer;

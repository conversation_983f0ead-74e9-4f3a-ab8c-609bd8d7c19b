import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
    TblProfiles,
    BasicInfoModel,
    PersonalInfoModel,
    EducationCareerModel,
    FamilyInfoModel,
    ContactInfoModel
} from "../../Types/ProfileTypes";
import { ProfileService } from "../../Services/ProfileService";

interface IProfileSliceState {
    profiles: TblProfiles[];
    currentProfile: TblProfiles | null;
    isLoading: boolean;
    error: string | null;
    totalCount: number;
    currentStep: number;
    isEditing: boolean;
}

const initialState: IProfileSliceState = {
    profiles: [],
    currentProfile: null,
    isLoading: false,
    error: null,
    totalCount: 0,
    currentStep: 0,
    isEditing: false
};

// Async thunks
export const fetchUserProfiles = createAsyncThunk(
    'profile/fetchUserProfiles',
    async (_, { rejectWithValue }) => {
        try {
            const profiles = await ProfileService.getUserProfiles();
            return profiles;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch profiles');
        }
    }
);

export const fetchProfile = createAsyncThunk(
    'profile/fetchProfile',
    async (profileId: number, { rejectWithValue }) => {
        try {
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to fetch profile');
        }
    }
);

export const createProfile = createAsyncThunk(
    'profile/createProfile',
    async (basicInfo: BasicInfoModel, { rejectWithValue }) => {
        try {
            const response = await ProfileService.createProfile(basicInfo);
            // Extract profile ID from response and fetch the created profile
            const profileId = Object.values(response)[0] as number;
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to create profile');
        }
    }
);

export const updatePersonalInfo = createAsyncThunk(
    'profile/updatePersonalInfo',
    async ({ profileId, personalInfo }: { profileId: number; personalInfo: PersonalInfoModel }, { rejectWithValue }) => {
        try {
            await ProfileService.updatePersonalInfo(profileId, personalInfo);
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to update personal info');
        }
    }
);

export const updateEducationCareer = createAsyncThunk(
    'profile/updateEducationCareer',
    async ({ profileId, educationCareer }: { profileId: number; educationCareer: EducationCareerModel }, { rejectWithValue }) => {
        try {
            await ProfileService.updateEducationCareer(profileId, educationCareer);
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to update education career');
        }
    }
);

export const updateFamilyInfo = createAsyncThunk(
    'profile/updateFamilyInfo',
    async ({ profileId, familyInfo }: { profileId: number; familyInfo: FamilyInfoModel }, { rejectWithValue }) => {
        try {
            await ProfileService.updateFamilyInfo(profileId, familyInfo);
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to update family info');
        }
    }
);

export const updateContactInfo = createAsyncThunk(
    'profile/updateContactInfo',
    async ({ profileId, contactInfo }: { profileId: number; contactInfo: ContactInfoModel }, { rejectWithValue }) => {
        try {
            await ProfileService.updateContactInfo(profileId, contactInfo);
            const profile = await ProfileService.getProfile(profileId);
            return profile;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.message || 'Failed to update contact info');
        }
    }
);

const profileSlice = createSlice({
    name: "profile",
    initialState,
    reducers: {
        setCurrentProfile: (state, action: PayloadAction<TblProfiles | null>) => {
            state.currentProfile = action.payload;
        },
        setCurrentStep: (state, action: PayloadAction<number>) => {
            state.currentStep = action.payload;
        },
        setIsEditing: (state, action: PayloadAction<boolean>) => {
            state.isEditing = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        },
        resetProfileState: (state) => {
            state.currentProfile = null;
            state.currentStep = 0;
            state.isEditing = false;
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        // Fetch user profiles
        builder
            .addCase(fetchUserProfiles.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchUserProfiles.fulfilled, (state, action: PayloadAction<TblProfiles[]>) => {
                state.isLoading = false;
                state.profiles = action.payload;
                state.totalCount = action.payload.length;
            })
            .addCase(fetchUserProfiles.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Fetch single profile
        builder
            .addCase(fetchProfile.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchProfile.fulfilled, (state, action: PayloadAction<TblProfiles>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
            })
            .addCase(fetchProfile.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Create profile
        builder
            .addCase(createProfile.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(createProfile.fulfilled, (state, action: PayloadAction<TblProfiles>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
                state.profiles.push(action.payload);
                state.totalCount += 1;
            })
            .addCase(createProfile.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Update personal info
        builder
            .addCase(updatePersonalInfo.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(updatePersonalInfo.fulfilled, (state, action: PayloadAction<TblProfiles>) => {
                state.isLoading = false;
                state.currentProfile = action.payload;
                const index = state.profiles.findIndex(p => p.id === action.payload.id);
                if (index !== -1) {
                    state.profiles[index] = action.payload;
                }
            })
            .addCase(updatePersonalInfo.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            });

        // Update education career
        builder
            .addCase(updateEducationCareer.fulfilled, (state, action: PayloadAction<TblProfiles>) => {
                state.currentProfile = action.payload;
                const index = state.profiles.findIndex(p => p.id === action.payload.id);
                if (index !== -1) {
                    state.profiles[index] = action.payload;
                }
            });

        // Update family info
        builder
            .addCase(updateFamilyInfo.fulfilled, (state, action: PayloadAction<TblProfiles>) => {
                state.currentProfile = action.payload;
                const index = state.profiles.findIndex(p => p.id === action.payload.id);
                if (index !== -1) {
                    state.profiles[index] = action.payload;
                }
            });

        // Update contact info
        builder
            .addCase(updateContactInfo.fulfilled, (state, action: PayloadAction<TblProfiles>) => {
                state.currentProfile = action.payload;
                const index = state.profiles.findIndex(p => p.id === action.payload.id);
                if (index !== -1) {
                    state.profiles[index] = action.payload;
                }
            });
    }
});

export const { 
    setCurrentProfile, 
    setCurrentStep, 
    setIsEditing, 
    clearError, 
    resetProfileState 
} = profileSlice.actions;

export default profileSlice.reducer;

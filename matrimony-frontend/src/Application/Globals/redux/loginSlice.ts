import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface ILoginStateLoggedIn {
    loggedIn: true;
    profilePictureURL?: string;
    name: string;
}

interface ILoginStateLoggedOut {
    loggedIn: false;
}

type ILoginState = ILoginStateLoggedIn | ILoginStateLoggedOut;

interface ILoginValue {
    value: ILoginState
}

const initialState: ILoginValue = {
    value : {
        loggedIn: false
    }
};

const loginSlice = createSlice({
    name: "login",
    initialState,
    reducers: {
        setLogin: (state, payload: PayloadAction<ILoginState>) => {
            state.value = payload.payload
        }
    }
});

export const { setLogin } = loginSlice.actions;
export default loginSlice.reducer;

import { FC, useEffect, useState, useCallback } from 'react';
import {
    Box,
    Stepper,
    Step,
    StepL<PERSON>l,
    StepContent,
    Button,
    Typography,
    Paper,
    Alert,
    CircularProgress,
    Card,
    CardContent
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../Globals/redux/hooks';
import {
    fetchProfile,
    createProfile,
    updatePersonalInfo,
    updateEducationCareer,
    updateFamilyInfo,
    updateContactInfo,
    setCurrentStep,
    setIsEditing,
    resetProfileState
} from '../Globals/redux/profileSlice';
import {
    ProfileCompletenessStep,
    PROFILE_STEPS,
    getStepIndex,
    BasicInfoModel
} from '../Types/ProfileTypes';
import BasicInfoForm from '../Components/Profile/BasicInfoForm';

interface IProfileStepperPageProps {
    profileId?: number;
    isEditing?: boolean;
}

const ProfileStepperPage: FC<IProfileStepperPageProps> = ({
    profileId: propProfileId,
    isEditing: propIsEditing = false
}) => {
    const navigate = useNavigate();
    const { profileId: paramProfileId } = useParams<{ profileId: string }>();
    const dispatch = useAppDispatch();

    const profileId = propProfileId || (paramProfileId ? parseInt(paramProfileId) : undefined);
    const isEditing = propIsEditing || Boolean(profileId);

    const {
        currentProfile,
        isLoading,
        error
    } = useAppSelector(state => state.profile);

    const [activeStep, setActiveStep] = useState(0);
    const [isStepValid, setIsStepValid] = useState(false);

    useEffect(() => {
        if (profileId && isEditing) {
            dispatch(fetchProfile(profileId));
        } else {
            dispatch(resetProfileState());
        }
        dispatch(setIsEditing(isEditing));

        return () => {
            dispatch(resetProfileState());
        };
    }, [dispatch, profileId, isEditing]);

    useEffect(() => {
        if (currentProfile) {
            const stepIndex = getStepIndex(currentProfile.profileCompletenessStep || ProfileCompletenessStep.BASIC_INFO);
            setActiveStep(stepIndex >= 0 ? stepIndex : 0);
            dispatch(setCurrentStep(stepIndex >= 0 ? stepIndex : 0));
        }
    }, [currentProfile, dispatch]);

    const handleNext = useCallback(async () => {
        if (activeStep < PROFILE_STEPS.length - 1) {
            const nextStep = activeStep + 1;
            setActiveStep(nextStep);
            dispatch(setCurrentStep(nextStep));
        } else {
            // Profile completed
            if (currentProfile?.id) {
                navigate(`/profile/${currentProfile.id}/home`);
            }
        }
    }, [activeStep, currentProfile, dispatch, navigate]);

    const handleBack = useCallback(() => {
        if (activeStep > 0) {
            const prevStep = activeStep - 1;
            setActiveStep(prevStep);
            dispatch(setCurrentStep(prevStep));
        }
    }, [activeStep, dispatch]);

    const handleStepSubmit = useCallback(async (stepData: any) => {
        try {
            if (currentProfile?.id) {
                // Update existing profile based on current step
                const currentState = PROFILE_STEPS[activeStep].state;

                switch (currentState) {
                    case ProfileCompletenessStep.PERSONAL_INFO:
                        await dispatch(updatePersonalInfo({
                            profileId: currentProfile.id,
                            personalInfo: stepData
                        })).unwrap();
                        break;
                    case ProfileCompletenessStep.EDUCATION_CAREER:
                        await dispatch(updateEducationCareer({
                            profileId: currentProfile.id,
                            educationCareer: stepData
                        })).unwrap();
                        break;
                    case ProfileCompletenessStep.FAMILY_INFO:
                        await dispatch(updateFamilyInfo({
                            profileId: currentProfile.id,
                            familyInfo: stepData
                        })).unwrap();
                        break;
                    case ProfileCompletenessStep.CONTACT_INFO:
                        await dispatch(updateContactInfo({
                            profileId: currentProfile.id,
                            contactInfo: stepData
                        })).unwrap();
                        break;
                }
            } else {
                // Create new profile with basic info
                const newProfile = await dispatch(createProfile(stepData as BasicInfoModel)).unwrap();

                if (newProfile.id) {
                    navigate(`/profile/${newProfile.id}/create`);
                    return;
                }
            }

            handleNext();
        } catch (error) {
            console.error('Failed to save profile step:', error);
        }
    }, [currentProfile, activeStep, dispatch, handleNext, navigate]);

    const renderStepContent = useCallback((step: number) => {
        const stepConfig = PROFILE_STEPS[step];
        
        switch (stepConfig.state) {
            case ProfileCompletenessStep.BASIC_INFO:
                return (
                    <BasicInfoForm
                        initialValues={{
                            name: currentProfile?.name || '',
                            gender: currentProfile?.gender,
                            dateOfBirth: currentProfile?.dateOfBirth || '',
                            placeOfBirth: currentProfile?.placeOfBirth || '',
                            birthLatitude: currentProfile?.birthLatitude,
                            birthLongitude: currentProfile?.birthLongitude,
                            timeOfBirth: currentProfile?.timeOfBirth,
                            timeZone: currentProfile?.timeZone
                        }}
                        onSubmit={handleStepSubmit}
                        onValidationChange={setIsStepValid}
                        isReadOnly={false}
                    />
                );

            case ProfileCompletenessStep.PERSONAL_INFO:
                return (
                    <Box sx={{ p: 2 }}>
                        <Typography>Personal Information Form - Coming Soon</Typography>
                        <Button onClick={() => handleStepSubmit({})}>
                            Continue
                        </Button>
                    </Box>
                );
            
            case ProfileCompletenessStep.EDUCATION_CAREER:
                return (
                    <Box sx={{ p: 2 }}>
                        <Typography>Education & Career Form - Coming Soon</Typography>
                        <Button onClick={() => handleStepSubmit({})}>
                            Continue
                        </Button>
                    </Box>
                );

            case ProfileCompletenessStep.FAMILY_INFO:
                return (
                    <Box sx={{ p: 2 }}>
                        <Typography>Family Information Form - Coming Soon</Typography>
                        <Button onClick={() => handleStepSubmit({})}>
                            Continue
                        </Button>
                    </Box>
                );

            case ProfileCompletenessStep.CONTACT_INFO:
                return (
                    <Box sx={{ p: 2 }}>
                        <Typography>Contact Information Form - Coming Soon</Typography>
                        <Button onClick={() => handleStepSubmit({})}>
                            Complete Profile
                        </Button>
                    </Box>
                );
            
            default:
                return <Typography>Unknown step</Typography>;
        }
    }, [currentProfile, handleStepSubmit]);

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
                <CircularProgress />
            </Box>
        );
    }

    return (
        <Box sx={{ p: 3 }}>
            <Card>
                <CardContent>
                    <Typography variant="h4" gutterBottom align="center">
                        {isEditing ? 'Edit Profile' : 'Create Your Profile'}
                    </Typography>
                    
                    {error && (
                        <Alert severity="error" sx={{ mb: 3 }}>
                            {error}
                        </Alert>
                    )}

                    <Stepper activeStep={activeStep} orientation="vertical">
                        {PROFILE_STEPS.map((step, index) => (
                            <Step key={step.state}>
                                <StepLabel>
                                    <Typography variant="h6">{step.label}</Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {step.description}
                                    </Typography>
                                </StepLabel>
                                <StepContent>
                                    <Box sx={{ mb: 2 }}>
                                        {renderStepContent(index)}
                                    </Box>
                                    <Box sx={{ mb: 1 }}>
                                        <Button
                                            variant="contained"
                                            onClick={handleNext}
                                            sx={{ mt: 1, mr: 1 }}
                                            disabled={!isStepValid && index === 0}
                                        >
                                            {index === PROFILE_STEPS.length - 1 ? 'Complete' : 'Continue'}
                                        </Button>
                                        <Button
                                            disabled={index === 0}
                                            onClick={handleBack}
                                            sx={{ mt: 1, mr: 1 }}
                                        >
                                            Back
                                        </Button>
                                    </Box>
                                </StepContent>
                            </Step>
                        ))}
                    </Stepper>

                    {activeStep === PROFILE_STEPS.length && (
                        <Paper square elevation={0} sx={{ p: 3 }}>
                            <Typography variant="h6" gutterBottom>
                                Profile Completed Successfully!
                            </Typography>
                            <Typography variant="body1" sx={{ mb: 2 }}>
                                Your profile has been created and is ready for review.
                            </Typography>
                            <Button
                                onClick={() => navigate('/profiles')}
                                variant="contained"
                            >
                                View All Profiles
                            </Button>
                        </Paper>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
};

export default ProfileStepperPage;

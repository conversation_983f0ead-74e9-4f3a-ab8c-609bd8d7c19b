import {FC} from "react";
import {BasicInfoModel} from "@hingua/matrimony-ts-client";
import {profileApi} from "../../api";

interface BasicInfoModelCreate {
    isEditing: false;
}

interface BasicInfoModelEdit {
    isEditing: false;
    profileId: number;
}

type BasicInfoProps = BasicInfoModelCreate | BasicInfoModelEdit;


const BasicInfo: FC<BasicInfoProps> = ({isEditing}) => {

    return <>
        <h1>
            {`${isEditing} Basic Info - 1`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1><h1>
        {`${isEditing} Basic Info`}
    </h1>
        <h1>
            {`${isEditing} Basic Info`}
        </h1>


    </>;
}

export default BasicInfo;
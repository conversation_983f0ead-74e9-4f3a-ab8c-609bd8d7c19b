import * as Yup from 'yup';

export const getSignUpValidationSchema = (t:any) => {
    return Yup.object().shape({
        name: Yup.string()
            .required(t('name_required')),

        email: Yup.string()
            .email(t('email_invalid'))
            .required(t('email_required')),

        mobile: Yup.string()
            .matches(/^[0-9]{10}$/, t('mobile_invalid'))
            .required(t('mobile_required')),

        password: Yup.string()
            .min(8, t('password_min'))
            .matches(/[a-z]/, t('password_lowercase'))
            .matches(/[A-Z]/, t('password_uppercase'))
            .matches(/[0-9]/, t('password_number'))
            .matches(/[!@#$%^&*()\-_+={}[\]|\\/?<>,.~`;:'"]/, t('password_special'))
            .required(t('password_required')),

        confirm_password: Yup.string()
            .oneOf([Yup.ref('password'), ''], t('confirm_password_match'))
            .required(t('confirm_password_required')),
    });
}


export const getLoginValidationSchema = (t:any) => {
    return Yup.object().shape({
        username: Yup.string()
            .required(t('email_mobile_required')),
        password: Yup.string()
            .required(t('password_required')),
    });
}

// Profile validation schemas
export const getBasicInfoValidationSchema = () => {
    return Yup.object().shape({
        firstName: Yup.string()
            .min(2, 'First name must be at least 2 characters')
            .max(50, 'First name must be less than 50 characters')
            .required('First name is required'),
        lastName: Yup.string()
            .min(2, 'Last name must be at least 2 characters')
            .max(50, 'Last name must be less than 50 characters')
            .required('Last name is required'),
        dateOfBirth: Yup.date()
            .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old')
            .required('Date of birth is required'),
        gender: Yup.string()
            .oneOf(['MALE', 'FEMALE', 'OTHER'], 'Please select a valid gender')
            .required('Gender is required'),
        religion: Yup.string()
            .required('Religion is required'),
        caste: Yup.string()
            .required('Caste is required'),
        motherTongue: Yup.string()
            .required('Mother tongue is required'),
        maritalStatus: Yup.string()
            .oneOf(['NEVER_MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED'], 'Please select a valid marital status')
            .required('Marital status is required')
    });
}

export const getPersonalInfoValidationSchema = () => {
    return Yup.object().shape({
        height: Yup.number()
            .min(100, 'Height must be at least 100 cm')
            .max(250, 'Height must be less than 250 cm')
            .required('Height is required'),
        weight: Yup.number()
            .min(30, 'Weight must be at least 30 kg')
            .max(200, 'Weight must be less than 200 kg'),
        bodyType: Yup.string()
            .oneOf(['SLIM', 'AVERAGE', 'ATHLETIC', 'HEAVY'], 'Please select a valid body type')
            .required('Body type is required'),
        complexion: Yup.string()
            .oneOf(['VERY_FAIR', 'FAIR', 'WHEATISH', 'DARK', 'VERY_DARK'], 'Please select a valid complexion')
            .required('Complexion is required'),
        diet: Yup.string()
            .oneOf(['VEGETARIAN', 'NON_VEGETARIAN', 'EGGETARIAN', 'VEGAN'], 'Please select a valid diet preference')
            .required('Diet preference is required'),
        smoking: Yup.string()
            .oneOf(['NEVER', 'OCCASIONALLY', 'REGULARLY'], 'Please select a valid smoking habit')
            .required('Smoking habit is required'),
        drinking: Yup.string()
            .oneOf(['NEVER', 'OCCASIONALLY', 'REGULARLY'], 'Please select a valid drinking habit')
            .required('Drinking habit is required')
    });
}

// const PASSWORD_REGEX = /^(?=.*[A-Z])(?=.*[!@#$%^&*()\-_+={}[\]|\\/?<>,.~`;:'"])(?=.*[0-9])(?=.*[a-z]).{8,}$/;
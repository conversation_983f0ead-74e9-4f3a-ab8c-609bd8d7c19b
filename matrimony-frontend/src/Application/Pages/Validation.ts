import * as Yup from 'yup';

export const getSignUpValidationSchema = (t:any) => {
    return Yup.object().shape({
        name: Yup.string()
            .required(t('name_required')),

        email: Yup.string()
            .email(t('email_invalid'))
            .required(t('email_required')),

        mobile: Yup.string()
            .matches(/^[0-9]{10}$/, t('mobile_invalid'))
            .required(t('mobile_required')),

        password: Yup.string()
            .min(8, t('password_min'))
            .matches(/[a-z]/, t('password_lowercase'))
            .matches(/[A-Z]/, t('password_uppercase'))
            .matches(/[0-9]/, t('password_number'))
            .matches(/[!@#$%^&*()\-_+={}[\]|\\/?<>,.~`;:'"]/, t('password_special'))
            .required(t('password_required')),

        confirm_password: Yup.string()
            .oneOf([Yup.ref('password'), ''], t('confirm_password_match'))
            .required(t('confirm_password_required')),
    });
}


export const getLoginValidationSchema = (t:any) => {
    return Yup.object().shape({
        username: Yup.string()
            .required(t('email_mobile_required')),
        password: Yup.string()
            .required(t('password_required')),
    });
}


// const PASSWORD_REGEX = /^(?=.*[A-Z])(?=.*[!@#$%^&*()\-_+={}[\]|\\/?<>,.~`;:'"])(?=.*[0-9])(?=.*[a-z]).{8,}$/;
import {FC, useCallback, useMemo, useState} from "react";
import {Card, CardContent, Box, Typography, TextField, Button} from "@mui/material";
import HinguaGrid from "../CommonComponents/HinguaGrid.tsx";
import logo from "../../assets/logo.png"
import {useTranslation} from "react-i18next";
import {useFormik} from "formik";
import {getLoginValidationSchema, getSignUpValidationSchema} from "./Validation.ts";
import { AuthService } from "../Services/ProfileService";


const LoginSignUpPage: FC<unknown> = () => {
    const {t} = useTranslation();
    const {t: validationT} = useTranslation("validation");
    const [type, setType] = useState<"login" | "signup">("login");

    const title = useMemo(() => {
        return {
            element: <Typography variant="h4">
                {t("title")}
            </Typography>,
            properties: {
                alignSelf: "center"
            }
        };
    }, [t]);

    const registerUser = useCallback(async(values:any) => {
        const {name, email, mobile, password} = values;
        try {
            const result = await AuthService.register({ name, email, mobile, password });
            console.log(result);
            if(result.status === "ok") {
                setType("login");
                alert("Registration successful! Please login.");
            } else {
                alert("Registration failed. Please try again.");
            }
        } catch (error) {
            console.error(error)
            alert("Registration failed. Please try again.");
        }
    }, [setType])

    const login = useCallback(async(values:any) => {
        const {username, password} = values;
        try {
            const result = await AuthService.login({ username, password });
            if(result.status === "ok") {
                // Refresh the page to trigger auth status check and profile workflow
                window.location.href = "/";
            } else {
                alert("Incorrect Credentials");
            }
        } catch (error) {
            console.error(error)
            alert("Login failed. Please try again.");
        }
    }, [])


    const loginFormik = useFormik({
        initialValues: {
            username: '',
            password: '',
        },
        validationSchema: getLoginValidationSchema(validationT),
        onSubmit: login
    });

    const signUpFormik = useFormik({
        initialValues: {
            name: '',
            email: '',
            mobile: '',
            password: '',
            confirm_password: ''
        },
        validationSchema: getSignUpValidationSchema(validationT),
        onSubmit: registerUser
    });


    const formContent = useMemo(() => {
        if (type === "signup") {
            return <form onSubmit={signUpFormik.handleSubmit} key={"signup"}>
                <HinguaGrid
                    direction="column"
                    spacing={2}
                    key={"signup"}
                    gridItems={[
                        title,
                        {
                            element: <TextField
                                fullWidth
                                label={t("name")}
                                name={"name"}
                                required
                                value={signUpFormik.values.name}
                                onChange={signUpFormik.handleChange}
                                onBlur={signUpFormik.handleBlur}
                                error={signUpFormik.touched.name && Boolean(signUpFormik.errors.name)}
                                helperText={signUpFormik.touched.name && signUpFormik.errors.name}
                            />,
                            properties: {}
                        },
                        {
                            element: <TextField
                                fullWidth
                                label={t("email")}
                                name={"email"}
                                required
                                value={signUpFormik.values.email}
                                onChange={signUpFormik.handleChange}
                                onBlur={signUpFormik.handleBlur}
                                error={signUpFormik.touched.email && Boolean(signUpFormik.errors.email)}
                                helperText={signUpFormik.touched.email && signUpFormik.errors.email}
                            />,
                            properties: {}
                        },
                        {
                            element: <TextField
                                fullWidth
                                label={t("mobile_number")}
                                name={"mobile"}
                                required
                                value={signUpFormik.values.mobile}
                                onChange={signUpFormik.handleChange}
                                onBlur={signUpFormik.handleBlur}
                                error={signUpFormik.touched.mobile && Boolean(signUpFormik.errors.mobile)}
                                helperText={signUpFormik.touched.mobile && signUpFormik.errors.mobile}
                            />,
                            properties: {}
                        },
                        {
                            element: <TextField
                                fullWidth
                                type="password"
                                label={t("password")}
                                name={"password"}
                                required
                                value={signUpFormik.values.password}
                                onChange={signUpFormik.handleChange}
                                onBlur={signUpFormik.handleBlur}
                                error={signUpFormik.touched.password && Boolean(signUpFormik.errors.password)}
                                helperText={signUpFormik.touched.password && signUpFormik.errors.password}
                            />,
                            properties: {}
                        },
                        {
                            element: <TextField
                                fullWidth
                                type="password"
                                label={t("confirm_password")}
                                name={"confirm_password"}
                                required
                                value={signUpFormik.values.confirm_password}
                                onChange={signUpFormik.handleChange}
                                onBlur={signUpFormik.handleBlur}
                                error={signUpFormik.touched.confirm_password && Boolean(signUpFormik.errors.confirm_password)}
                                helperText={signUpFormik.touched.confirm_password && signUpFormik.errors.confirm_password}
                            />,
                            properties: {}
                        },
                        {
                            element: <Button size="large" type="submit">
                                {t("sign_up")}
                            </Button>,
                            properties: {
                                alignSelf: "center"
                            }
                        },
                        {
                            element: <Button onClick={() => setType("login")} variant="text" size="large">
                                {t("login")}
                            </Button>,
                            properties: {
                                alignSelf: "center"
                            }
                        }
                    ]}
                />
            </form>;
        } else {
            return <form
                onSubmit={loginFormik.handleSubmit}
                key={"login"}
            >
                <HinguaGrid
                    direction="column"
                    spacing={2}
                    key={"login"}
                    gridItems={[
                        title,
                        {
                            element: <TextField
                                fullWidth
                                label={`${t("email")} / ${t("mobile_number")}`}
                                name={"username"}
                                required
                                autoComplete={"username"}
                                value={loginFormik.values.username}
                                onChange={loginFormik.handleChange}
                                onBlur={loginFormik.handleBlur}
                                error={loginFormik.touched.username && Boolean(loginFormik.errors.username)}
                                helperText={loginFormik.touched.username && loginFormik.errors.username}
                            />,
                            properties: {}
                        },
                        {
                            element: <TextField
                                fullWidth
                                type="password"
                                label={t("password")}
                                name={"password"}
                                autoComplete={"current-password"}
                                required
                                value={loginFormik.values.password}
                                onChange={loginFormik.handleChange}
                                onBlur={loginFormik.handleBlur}
                                error={loginFormik.touched.password && Boolean(loginFormik.errors.password)}
                                helperText={loginFormik.touched.password && loginFormik.errors.password}
                            />,
                            properties: {}
                        },
                        {
                            element: <Button size="large" type="submit">
                                {t("login")}
                            </Button>,
                            properties: {
                                alignSelf: "center"
                            }
                        },
                        {
                            element: <Button onClick={() => setType("signup")} variant="text" size="large">
                                {t("sign_up")}
                            </Button>,
                            properties: {
                                alignSelf: "center"
                            }
                        }
                    ]}
                />
            </form>;
        }
    }, [type, setType, loginFormik, signUpFormik, t, title])

    return <Box
        sx={() => ({
            backgroundColor: "#B6AA8E",
            backgroundImage: `url(${logo})`,
            backgroundRepeat: "no-repeat",
            backgroundSize: "105vh",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            position: "fixed"
        })}
    >
        <Card
            raised={true}
            sx={{
                position: "fixed",
                left: "80%",
                top: "50%",
                transform: "translate(-50%, -50%)",
                minWidth: "30vw",
                minHeight: "60vh"
            }}
        >
            <CardContent>
                {formContent}
            </CardContent>

        </Card>
    </Box>;
}

export default LoginSignUpPage;
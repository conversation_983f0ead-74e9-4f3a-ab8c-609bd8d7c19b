import React, { FC, useEffect, useCallback } from 'react';
import {
    Box,
    Card,
    CardContent,
    Ty<PERSON>graphy,
    Button,
    Grid2,
    Avatar,
    Chip,
    LinearProgress,
    Alert,
    CircularProgress,
    IconButton,
    Menu,
    MenuItem,
    ListItemIcon,
    ListItemText
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Home as HomeIcon,
    MoreVert as MoreVertIcon,
    Delete as DeleteIcon,
    Visibility as VisibilityIcon,
    VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../Globals/redux/hooks';
import { fetchUserProfiles } from '../Globals/redux/profileSlice';
import { TblProfiles, ProfileCompletenessStep } from '../Types/ProfileTypes';

const ProfileListPage: FC = () => {
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    
    const {
        profiles,
        isLoading,
        error
    } = useAppSelector(state => state.profile);

    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const [selectedProfile, setSelectedProfile] = React.useState<TblProfiles | null>(null);

    useEffect(() => {
        dispatch(fetchUserProfiles());
    }, [dispatch]);

    const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, profile: TblProfiles) => {
        setAnchorEl(event.currentTarget);
        setSelectedProfile(profile);
    }, []);

    const handleMenuClose = useCallback(() => {
        setAnchorEl(null);
        setSelectedProfile(null);
    }, []);

    const handleCreateProfile = useCallback(() => {
        navigate('/profile/create');
    }, [navigate]);

    const handleViewProfile = useCallback((profile: TblProfiles) => {
        if (profile.profileCompletenessStep === ProfileCompletenessStep.COMPLETED) {
            navigate(`/profile/${profile.id}/home`);
        } else {
            navigate(`/profile/${profile.id}/create`);
        }
        handleMenuClose();
    }, [navigate, handleMenuClose]);

    const handleEditProfile = useCallback((profile: TblProfiles) => {
        navigate(`/profile/${profile.id}/edit`);
        handleMenuClose();
    }, [navigate, handleMenuClose]);

    const getProfileStateLabel = useCallback((state: ProfileCompletenessStep) => {
        switch (state) {
            case ProfileCompletenessStep.BASIC_INFO:
                return 'Basic Info';
            case ProfileCompletenessStep.PERSONAL_INFO:
                return 'Personal Info';
            case ProfileCompletenessStep.EDUCATION_CAREER:
                return 'Education & Career';
            case ProfileCompletenessStep.FAMILY_INFO:
                return 'Family Info';
            case ProfileCompletenessStep.CONTACT_INFO:
                return 'Contact Info';
            case ProfileCompletenessStep.COMPLETED:
                return 'Completed';
            default:
                return 'Unknown';
        }
    }, []);

    const getProfileStateColor = useCallback((state: ProfileCompletenessStep) => {
        switch (state) {
            case ProfileCompletenessStep.COMPLETED:
                return 'success';
            case ProfileCompletenessStep.CONTACT_INFO:
                return 'warning';
            default:
                return 'primary';
        }
    }, []);

    const renderProfileCard = useCallback((profile: TblProfiles) => {
        const fullName = profile.basicInfo 
            ? `${profile.basicInfo.firstName} ${profile.basicInfo.lastName}`
            : 'Unnamed Profile';

        return (
            <Grid2 key={profile.id} size={{ xs: 12, sm: 6, md: 4 }}>
                <Card 
                    sx={{ 
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative'
                    }}
                >
                    <CardContent sx={{ flexGrow: 1 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                            <Box display="flex" alignItems="center" gap={2}>
                                <Avatar
                                    src={profile.profilePicture}
                                    sx={{ width: 60, height: 60 }}
                                >
                                    {fullName.charAt(0)}
                                </Avatar>
                                <Box>
                                    <Typography variant="h6" component="h2">
                                        {fullName}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {profile.basicInfo?.gender} • {profile.basicInfo?.religion}
                                    </Typography>
                                </Box>
                            </Box>
                            <IconButton
                                onClick={(e) => handleMenuOpen(e, profile)}
                                size="small"
                            >
                                <MoreVertIcon />
                            </IconButton>
                        </Box>

                        <Box mb={2}>
                            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                                <Typography variant="body2" color="text.secondary">
                                    Profile Completion
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                    {profile.completionPercentage}%
                                </Typography>
                            </Box>
                            <LinearProgress 
                                variant="determinate" 
                                value={profile.completionPercentage} 
                                sx={{ height: 8, borderRadius: 4 }}
                            />
                        </Box>

                        <Box display="flex" gap={1} mb={2} flexWrap="wrap">
                            <Chip
                                label={getProfileStateLabel(profile.profileCompletenessStep)}
                                color={getProfileStateColor(profile.profileCompletenessStep)}
                                size="small"
                            />
                            <Chip
                                label={profile.isActive ? 'Active' : 'Inactive'}
                                color={profile.isActive ? 'success' : 'default'}
                                size="small"
                                variant="outlined"
                            />
                        </Box>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Created: {profile.createdAt ? new Date(profile.createdAt).toLocaleDateString() : 'Unknown'}
                        </Typography>

                        <Button
                            variant="contained"
                            fullWidth
                            onClick={() => handleViewProfile(profile)}
                            startIcon={profile.profileCompletenessStep === ProfileCompletenessStep.COMPLETED ? <HomeIcon /> : <EditIcon />}
                        >
                            {profile.profileCompletenessStep === ProfileCompletenessStep.COMPLETED ? 'View Profile' : 'Continue Setup'}
                        </Button>
                    </CardContent>
                </Card>
            </Grid2>
        );
    }, [handleMenuOpen, handleViewProfile, getProfileStateLabel, getProfileStateColor]);

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
                <CircularProgress />
            </Box>
        );
    }

    return (
        <Box sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h4" component="h1">
                    My Profiles
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleCreateProfile}
                >
                    Create New Profile
                </Button>
            </Box>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {profiles.length === 0 && !isLoading ? (
                <Card>
                    <CardContent sx={{ textAlign: 'center', py: 6 }}>
                        <Typography variant="h6" gutterBottom>
                            No Profiles Found
                        </Typography>
                        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                            Create your first profile to get started with finding your perfect match.
                        </Typography>
                        <Button
                            variant="contained"
                            size="large"
                            startIcon={<AddIcon />}
                            onClick={handleCreateProfile}
                        >
                            Create Your First Profile
                        </Button>
                    </CardContent>
                </Card>
            ) : (
                <Grid2 container spacing={3}>
                    {profiles.map(renderProfileCard)}
                </Grid2>
            )}

            <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
            >
                <MenuItem onClick={() => selectedProfile && handleViewProfile(selectedProfile)}>
                    <ListItemIcon>
                        {selectedProfile?.profileCompletenessStep === ProfileCompletenessStep.COMPLETED ? <HomeIcon /> : <EditIcon />}
                    </ListItemIcon>
                    <ListItemText>
                        {selectedProfile?.profileCompletenessStep === ProfileCompletenessStep.COMPLETED ? 'View Profile' : 'Continue Setup'}
                    </ListItemText>
                </MenuItem>
                <MenuItem onClick={() => selectedProfile && handleEditProfile(selectedProfile)}>
                    <ListItemIcon>
                        <EditIcon />
                    </ListItemIcon>
                    <ListItemText>Edit Profile</ListItemText>
                </MenuItem>
                <MenuItem onClick={handleMenuClose}>
                    <ListItemIcon>
                        {selectedProfile?.isActive ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </ListItemIcon>
                    <ListItemText>
                        {selectedProfile?.isActive ? 'Deactivate' : 'Activate'}
                    </ListItemText>
                </MenuItem>
                <MenuItem onClick={handleMenuClose}>
                    <ListItemIcon>
                        <DeleteIcon />
                    </ListItemIcon>
                    <ListItemText>Delete Profile</ListItemText>
                </MenuItem>
            </Menu>
        </Box>
    );
};

export default ProfileListPage;

import React, { FC, useEffect } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Avatar,
    Chip,
    Button,
    Grid2,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    CircularProgress
} from '@mui/material';
import {
    Edit as EditIcon,
    Person as PersonIcon,
    Work as WorkIcon,
    Family as FamilyIcon,
    Contact as ContactIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../Globals/redux/hooks';
import { fetchProfile } from '../Globals/redux/profileSlice';

const ProfileHomePage: FC = () => {
    const navigate = useNavigate();
    const { profileId } = useParams<{ profileId: string }>();
    const dispatch = useAppDispatch();
    
    const {
        currentProfile,
        isLoading,
        error
    } = useAppSelector(state => state.profile);

    useEffect(() => {
        if (profileId) {
            dispatch(fetchProfile(profileId));
        }
    }, [dispatch, profileId]);

    const handleEditProfile = () => {
        if (profileId) {
            navigate(`/profile/${profileId}/edit`);
        }
    };

    const renderBasicInfo = (profile: IProfile) => {
        if (!profile.basicInfo) return null;

        const { basicInfo } = profile;
        const fullName = `${basicInfo.firstName} ${basicInfo.lastName}`;
        const age = basicInfo.dateOfBirth 
            ? new Date().getFullYear() - new Date(basicInfo.dateOfBirth).getFullYear()
            : 'N/A';

        return (
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Box display="flex" alignItems="center" gap={3} mb={3}>
                        <Avatar
                            src={profile.profilePicture}
                            sx={{ width: 120, height: 120 }}
                        >
                            {fullName.charAt(0)}
                        </Avatar>
                        <Box>
                            <Typography variant="h4" gutterBottom>
                                {fullName}
                            </Typography>
                            <Typography variant="h6" color="text.secondary" gutterBottom>
                                {age} years • {basicInfo.gender}
                            </Typography>
                            <Box display="flex" gap={1} flexWrap="wrap">
                                <Chip label={basicInfo.religion} color="primary" />
                                <Chip label={basicInfo.caste} color="secondary" />
                                <Chip label={basicInfo.motherTongue} variant="outlined" />
                                <Chip 
                                    label={basicInfo.maritalStatus.replace('_', ' ')} 
                                    variant="outlined" 
                                />
                            </Box>
                        </Box>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Box>
                            <Typography variant="body2" color="text.secondary">
                                Profile Completion: {profile.completionPercentage}%
                            </Typography>
                            <Chip
                                label={profile.isActive ? 'Active' : 'Inactive'}
                                color={profile.isActive ? 'success' : 'default'}
                                size="small"
                                sx={{ mt: 1 }}
                            />
                        </Box>
                        <Button
                            variant="contained"
                            startIcon={<EditIcon />}
                            onClick={handleEditProfile}
                        >
                            Edit Profile
                        </Button>
                    </Box>
                </CardContent>
            </Card>
        );
    };

    const renderPersonalInfo = (profile: IProfile) => {
        if (!profile.personalInfo) return null;

        const { personalInfo } = profile;

        return (
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <PersonIcon color="primary" />
                        <Typography variant="h6">Personal Information</Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    
                    <Grid2 container spacing={2}>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Height</Typography>
                            <Typography variant="body1">{personalInfo.height} cm</Typography>
                        </Grid2>
                        {personalInfo.weight && (
                            <Grid2 size={6}>
                                <Typography variant="body2" color="text.secondary">Weight</Typography>
                                <Typography variant="body1">{personalInfo.weight} kg</Typography>
                            </Grid2>
                        )}
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Body Type</Typography>
                            <Typography variant="body1">{personalInfo.bodyType}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Complexion</Typography>
                            <Typography variant="body1">{personalInfo.complexion.replace('_', ' ')}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Diet</Typography>
                            <Typography variant="body1">{personalInfo.diet}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Smoking</Typography>
                            <Typography variant="body1">{personalInfo.smoking}</Typography>
                        </Grid2>
                    </Grid2>

                    {personalInfo.aboutMe && (
                        <Box mt={2}>
                            <Typography variant="body2" color="text.secondary">About Me</Typography>
                            <Typography variant="body1">{personalInfo.aboutMe}</Typography>
                        </Box>
                    )}
                </CardContent>
            </Card>
        );
    };

    const renderEducationCareer = (profile: IProfile) => {
        if (!profile.educationCareer) return null;

        const { educationCareer } = profile;

        return (
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <WorkIcon color="primary" />
                        <Typography variant="h6">Education & Career</Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    
                    <Grid2 container spacing={2}>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Education</Typography>
                            <Typography variant="body1">{educationCareer.highestEducation}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Occupation</Typography>
                            <Typography variant="body1">{educationCareer.occupation}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Employed In</Typography>
                            <Typography variant="body1">{educationCareer.employedIn}</Typography>
                        </Grid2>
                        {educationCareer.annualIncome && (
                            <Grid2 size={6}>
                                <Typography variant="body2" color="text.secondary">Annual Income</Typography>
                                <Typography variant="body1">₹{educationCareer.annualIncome.toLocaleString()}</Typography>
                            </Grid2>
                        )}
                        {educationCareer.workingCity && (
                            <Grid2 size={12}>
                                <Typography variant="body2" color="text.secondary">Working Location</Typography>
                                <Typography variant="body1">
                                    {educationCareer.workingCity}, {educationCareer.workingState}, {educationCareer.workingCountry}
                                </Typography>
                            </Grid2>
                        )}
                    </Grid2>
                </CardContent>
            </Card>
        );
    };

    const renderFamilyInfo = (profile: IProfile) => {
        if (!profile.familyInfo) return null;

        const { familyInfo } = profile;

        return (
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <FamilyIcon color="primary" />
                        <Typography variant="h6">Family Information</Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    
                    <Grid2 container spacing={2}>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Father</Typography>
                            <Typography variant="body1">{familyInfo.fatherName}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Mother</Typography>
                            <Typography variant="body1">{familyInfo.motherName}</Typography>
                        </Grid2>
                        <Grid2 size={4}>
                            <Typography variant="body2" color="text.secondary">Brothers</Typography>
                            <Typography variant="body1">{familyInfo.numberOfBrothers || 0}</Typography>
                        </Grid2>
                        <Grid2 size={4}>
                            <Typography variant="body2" color="text.secondary">Sisters</Typography>
                            <Typography variant="body1">{familyInfo.numberOfSisters || 0}</Typography>
                        </Grid2>
                        <Grid2 size={4}>
                            <Typography variant="body2" color="text.secondary">Family Type</Typography>
                            <Typography variant="body1">{familyInfo.familyType}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Family Status</Typography>
                            <Typography variant="body1">{familyInfo.familyStatus.replace('_', ' ')}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Family Values</Typography>
                            <Typography variant="body1">{familyInfo.familyValues}</Typography>
                        </Grid2>
                    </Grid2>

                    {familyInfo.aboutFamily && (
                        <Box mt={2}>
                            <Typography variant="body2" color="text.secondary">About Family</Typography>
                            <Typography variant="body1">{familyInfo.aboutFamily}</Typography>
                        </Box>
                    )}
                </CardContent>
            </Card>
        );
    };

    const renderContactInfo = (profile: IProfile) => {
        if (!profile.contactInfo) return null;

        const { contactInfo } = profile;

        return (
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <ContactIcon color="primary" />
                        <Typography variant="h6">Contact Information</Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    
                    <Grid2 container spacing={2}>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Email</Typography>
                            <Typography variant="body1">{contactInfo.email}</Typography>
                        </Grid2>
                        <Grid2 size={6}>
                            <Typography variant="body2" color="text.secondary">Mobile</Typography>
                            <Typography variant="body1">{contactInfo.mobile}</Typography>
                        </Grid2>
                        <Grid2 size={12}>
                            <Typography variant="body2" color="text.secondary">Address</Typography>
                            <Typography variant="body1">
                                {contactInfo.address}, {contactInfo.city}, {contactInfo.state} - {contactInfo.pincode}
                            </Typography>
                        </Grid2>
                    </Grid2>
                </CardContent>
            </Card>
        );
    };

    if (isLoading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">{error}</Alert>
            </Box>
        );
    }

    if (!currentProfile) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="warning">Profile not found</Alert>
            </Box>
        );
    }

    return (
        <Box sx={{ p: 3 }}>
            {renderBasicInfo(currentProfile)}
            {renderPersonalInfo(currentProfile)}
            {renderEducationCareer(currentProfile)}
            {renderFamilyInfo(currentProfile)}
            {renderContactInfo(currentProfile)}
        </Box>
    );
};

export default ProfileHomePage;

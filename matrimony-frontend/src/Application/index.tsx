import React, {useEffect} from 'react';
import ApplicationBar from './Components/ApplicationBar';
import axios from 'axios';
import {setLogin} from "./Globals/redux/loginSlice";
import {useAppDispatch, useAppSelector} from './Globals/redux/hooks';
import {Routes, Route, Navigate} from "react-router-dom";
import LoginSignUpPage from "./Pages/LoginSignUpPage.tsx";
import ProfileListPage from "./Pages/ProfileListPage.tsx";
import ProfileStepperPage from "./Pages/ProfileStepperPage.tsx";
import ProfileHomePage from "./Pages/ProfileHomePage.tsx";
import ProfileDashboard from "./Components/ProfileDashboard.tsx";

function isMobileDevice(): boolean {
    return /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

console.log(isMobileDevice())

const Application: React.FC<never> = () => {

    const dispatch = useAppDispatch();
    const login = useAppSelector(state => state.login.value);

    const apiCall = async () => {
        const result = await axios.post("/api/auth/status", null, {
            withCredentials: true
        })
        console.log(result.data)

        dispatch(setLogin(result.data))
    }

    useEffect(() => {
        apiCall();
    }, [])


    // Redirect to login if not logged in
    if (!login.loggedIn) {
        return <Routes>
            <Route path="/login" Component={LoginSignUpPage} />
            <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>;
    }

    return <Routes>
        <Route
            path="/login"
            Component={LoginSignUpPage}
        />

        {/* Profile Management Routes */}
        <Route
            path="/profiles"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileListPage />
                    </div>
                </>
            }
        />

        <Route
            path="/profile/create"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileStepperPage />
                    </div>
                </>
            }
        />

        <Route
            path="/profile/:profileId/create"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileStepperPage isEditing={false} />
                    </div>
                </>
            }
        />

        <Route
            path="/profile/:profileId/edit"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileStepperPage isEditing={true} />
                    </div>
                </>
            }
        />

        <Route
            path="/profile/:profileId/home"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileHomePage />
                    </div>
                </>
            }
        />

        {/* Default route - Profile Dashboard handles the logic */}
        <Route
            path="/"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileDashboard />
                    </div>
                </>
            }
        />

        <Route
            path="*"
            element={
                <>
                    <ApplicationBar/>
                    <div className='page-content'>
                        <ProfileDashboard />
                    </div>
                </>
            }
        />
    </Routes>
}

export default Application;
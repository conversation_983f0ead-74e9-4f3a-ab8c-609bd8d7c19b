import React, {useCallback, useEffect} from 'react';
import ApplicationBar from './Components/ApplicationBar';
import {setLogin} from "./Globals/redux/loginSlice";
import {useAppDispatch, useAppSelector} from './Globals/redux/hooks';
import {Routes, Route, Navigate, Outlet} from "react-router-dom";
import LoginSignUpPage from "./Pages/LoginSignUpPage.tsx";
import {authApi} from "./api.ts";
import BasicInfo from "./Pages/Profile/BasicInfo.tsx";

function isMobileDevice(): boolean {
    return /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

console.log(isMobileDevice())

const Application: React.FC<any> = () => {

    const dispatch = useAppDispatch();

    const apiCall = async () => {
        const result = await authApi.status();
        dispatch(setLogin(result.data))
    }

    useEffect(() => {
        apiCall();
    }, [])

    const login = useAppSelector(state => state.login.value)

    const handleRedirect = useCallback(() => {
        if (login.isLoggedIn) {
            if (login.activeProfile) {
                return <Navigate to="/home"/>;
            } else {
                return <Navigate to="/create-profile"/>;
            }
        } else {
            return <Navigate to="/"/>;
        }
    }, [login])


    return <Routes>
        <Route
            path="/login"
            Component={LoginSignUpPage}
        />

        <Route path="/" Component={() => <Outlet />}>
            <Route index element={<ApplicationBar />}/>
            <Route
                path={"/redirect"}
                Component={
                    () => <React.Fragment>
                        {handleRedirect()}
                    </React.Fragment>
                }
            />
        </Route>
        <Route path="/create-profile" Component={() => <BasicInfo isEditing={false}/> }/>
    </Routes>
}

export default Application;
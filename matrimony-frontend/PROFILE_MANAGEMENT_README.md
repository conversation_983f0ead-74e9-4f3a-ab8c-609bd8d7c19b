# Profile Management System

This document describes the comprehensive profile management system implemented for the matrimony application with MUI Stepper workflow.

## Overview

The profile management system handles the complete lifecycle of user profiles from creation to completion, following the specified workflow requirements:

- **No profiles**: Redirects to create profile workflow
- **One incomplete profile**: Takes user to next step for completion
- **Completed profile**: Goes to profile home page
- **Multiple profiles**: Shows profile list page

## Architecture

### Profile States
The system uses the following profile states managed through a stepper workflow:
- `BASIC_INFO`: Personal details and background
- `PERSONAL_INFO`: Physical attributes and lifestyle
- `EDUCATION_CAREER`: Educational and professional details
- `FAMILY_INFO`: Family background and values
- `CONTACT_INFO`: Contact details and preferences
- `COMPLETED`: Profile fully completed

### Component Structure (Disjoint Design)
Components are kept separate from the stepper flow for reusability in profile edits:

```
Components/Profile/
├── BasicInfoForm.tsx          # Reusable basic info form
├── PersonalInfoForm.tsx       # Personal information form (placeholder)
├── EducationCareerForm.tsx    # Education & career form (placeholder)
├── FamilyInfoForm.tsx         # Family information form (placeholder)
└── ContactInfoForm.tsx        # Contact information form (placeholder)
```

### Pages
```
Pages/
├── ProfileListPage.tsx        # Lists all user profiles
├── ProfileStepperPage.tsx     # Stepper workflow for create/edit
├── ProfileHomePage.tsx        # Displays completed profile
└── ProfileDashboard.tsx       # Handles workflow logic
```

## Key Features

### 1. Profile Workflow Logic
The `ProfileDashboard` component implements the main workflow:

```typescript
// No profiles - redirect to create
if (totalCount === 0) {
    navigate('/profile/create');
}

// One incomplete profile - continue setup
if (incompleteProfiles.length === 1 && profiles.length === 1) {
    navigate(`/profile/${incompleteProfile.id}/create`);
}

// Completed profiles - show first completed
if (completedProfiles.length > 0) {
    navigate(`/profile/${completedProfiles[0].id}/home`);
}
```

### 2. MUI Stepper Integration
- Vertical stepper with 5 steps
- Step validation and navigation
- Progress tracking
- Form state management

### 3. Redux State Management
- Profile slice with async thunks
- Loading and error states
- Profile CRUD operations
- Step progression tracking

### 4. API Integration
Comprehensive service layer for profile operations:
- `getUserProfiles()`: Get all user profiles
- `createProfile()`: Create new profile
- `updateProfile()`: Update existing profile
- `saveProfileStep()`: Save individual step data
- `updateProfileState()`: Progress through stepper

## Routing Structure

```
/                              → ProfileDashboard (handles workflow)
/profiles                      → ProfileListPage
/profile/create               → ProfileStepperPage (new profile)
/profile/:id/create           → ProfileStepperPage (continue setup)
/profile/:id/edit             → ProfileStepperPage (edit mode)
/profile/:id/home             → ProfileHomePage (view completed)
```

## Authentication Integration

- Redirects to login if not authenticated
- Fetches profiles after successful login
- Automatic workflow routing based on profile state

## Form Validation

Comprehensive validation schemas using Yup:
- Basic info validation (names, dates, required fields)
- Personal info validation (height, weight, enums)
- Type-safe form handling with Formik

## UI/UX Features

### Profile List Page
- Card-based profile display
- Completion percentage indicators
- Status chips (Active/Inactive, Profile State)
- Action menus (View, Edit, Activate/Deactivate, Delete)
- Empty state with call-to-action

### Profile Stepper Page
- Vertical stepper with descriptions
- Form validation per step
- Navigation controls (Next/Back/Complete)
- Loading states and error handling
- Edit mode support

### Profile Home Page
- Comprehensive profile display
- Organized sections with icons
- Edit profile functionality
- Profile picture support
- Responsive grid layout

## Data Types

Comprehensive TypeScript interfaces:
- `IProfile`: Main profile interface
- `IBasicInfo`, `IPersonalInfo`, etc.: Section-specific interfaces
- `ProfileState`: Enum for profile states
- `IProfileStepperState`: Stepper state management

## API Endpoints (Expected Backend)

```
GET    /api/profile/user-profiles     # Get user's profiles
GET    /api/profile/:id               # Get specific profile
POST   /api/profile/create            # Create new profile
PUT    /api/profile/update            # Update profile
PATCH  /api/profile/:id/state         # Update profile state
PATCH  /api/profile/:id/step          # Save step data
DELETE /api/profile/:id               # Delete profile
POST   /api/profile/:id/upload-picture # Upload profile picture
```

## Usage Flow

### 1. User Login
- User logs in successfully
- Application fetches user profiles
- ProfileDashboard determines next action

### 2. No Profiles Scenario
- Redirects to `/profile/create`
- Shows stepper starting at Basic Info
- User completes each step sequentially

### 3. Incomplete Profile Scenario
- Redirects to `/profile/:id/create`
- Stepper starts at current profile state
- User continues from where they left off

### 4. Completed Profile Scenario
- Redirects to `/profile/:id/home`
- Shows full profile view
- User can edit or manage profiles

### 5. Multiple Profiles Scenario
- Shows `/profiles` list page
- User can manage multiple profiles
- Create new or edit existing profiles

## Future Enhancements

### Immediate (Placeholders Created)
- Complete PersonalInfoForm component
- Complete EducationCareerForm component
- Complete FamilyInfoForm component
- Complete ContactInfoForm component

### Advanced Features
- Profile picture upload
- Profile matching/search
- Profile visibility settings
- Profile analytics
- Bulk profile operations
- Profile templates

## Testing

The system includes:
- Component-level testing setup
- Form validation testing
- Redux state testing
- API integration testing

## Development Status

✅ **Completed:**
- Profile types and interfaces
- Redux state management
- Basic routing and navigation
- Profile workflow logic
- BasicInfoForm component
- Profile list and home pages
- Authentication integration

🚧 **In Progress:**
- Additional form components (placeholders created)
- Profile picture upload
- Advanced validation

📋 **Planned:**
- Backend API implementation
- Profile matching features
- Advanced UI enhancements

## Running the Application

```bash
cd matrimony-frontend
npm run dev
```

The application will be available at `http://localhost:5174` (or next available port).

## Memory Note

Profile management workflow uses MUI Stepper with states BASIC_INFO, PERSONAL_INFO, EDUCATION_CAREER, FAMILY_INFO, CONTACT_INFO, COMPLETED, with components kept disjoint from stepper for reusability in profile edits.
